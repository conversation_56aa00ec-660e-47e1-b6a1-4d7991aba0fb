# 阮一峰技术周刊下载器

这是一个 Node.js 脚本，用于下载阮一峰老师的所有技术周刊 Markdown 文件到指定文件夹。

## 功能特点

- 🚀 自动下载所有周刊文件（从第1期到最新期）
- 📁 直接保存到指定文件夹，无需额外目录结构
- ⚡ 智能跳过已存在的文件，支持断点续传
- 📊 显示详细的下载进度和统计信息
- 🛡️ 错误处理和重试机制

## 使用方法

### 方法一：直接运行（推荐）

1. 确保你的电脑已安装 Node.js（版本 12 或以上）
2. 将 `download-weekly.cjs` 文件保存到任意位置
3. 打开命令行，切换到文件所在目录
4. 运行命令：

```bash
node download-weekly.cjs
```

### 方法二：使用 npm 脚本

1. 将 `standalone-package.json` 重命名为 `package.json`
2. 在命令行中运行：

```bash
npm start
```

或者：

```bash
npm run download
```

## 配置说明

默认下载目录是 `E:\阮一峰周刊`，如果需要修改，请编辑 `download-weekly.cjs` 文件中的 `TARGET_DIR` 变量：

```javascript
const TARGET_DIR = 'E:\\阮一峰周刊';  // 修改为你想要的路径
```

## 下载内容

脚本会下载所有以 `issue-` 开头的 Markdown 文件，包括：

- issue-1.md（第1期）
- issue-2.md（第2期）
- ...
- issue-361.md（第361期，最新）

## 注意事项

1. **网络连接**：需要稳定的网络连接访问 GitHub
2. **文件权限**：确保对目标目录有写入权限
3. **磁盘空间**：所有文件大约需要几十MB空间
4. **下载速度**：脚本会在每次下载间添加小延迟，避免请求过于频繁

## 运行示例

```
🚀 开始下载阮一峰技术周刊...
📁 目标目录: E:\阮一峰周刊
✅ 创建目录: E:\阮一峰周刊
📋 获取文件列表...
📄 找到 361 个周刊文件
⬇️  下载中 (1/361): issue-1.md
✅ 下载完成: issue-1.md
⬇️  下载中 (2/361): issue-2.md
✅ 下载完成: issue-2.md
...

📊 下载统计:
✅ 成功: 361 个文件
❌ 失败: 0 个文件
📁 文件保存在: E:\阮一峰周刊
🎉 所有文件下载完成！
```

## 故障排除

如果遇到下载失败：

1. 检查网络连接
2. 确认目标目录权限
3. 重新运行脚本（会自动跳过已下载的文件）

## 数据来源

所有数据来源于阮一峰老师的官方 GitHub 仓库：
https://github.com/ruanyf/weekly
