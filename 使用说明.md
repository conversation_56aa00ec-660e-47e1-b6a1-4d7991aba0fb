# 阮一峰技术周刊下载器

## 快速使用

1. **下载文件**：将 `download-ruanyf-weekly.js` 保存到任意文件夹
2. **运行命令**：在该文件夹打开命令行，执行：
   ```bash
   node download-ruanyf-weekly.js
   ```
3. **等待完成**：脚本会自动下载所有周刊到 `E:\阮一峰周刊` 文件夹

## 修改下载路径

如果想改变下载位置，编辑文件第6行：
```javascript
const TARGET_DIR = 'E:\\阮一峰周刊';  // 改成你想要的路径
```

例如改为：
```javascript
const TARGET_DIR = 'D:\\我的文档\\阮一峰周刊';
```

## 注意事项

- 需要安装 Node.js
- 需要网络连接访问 GitHub
- 首次下载约需几分钟
- 重复运行会跳过已下载的文件

## 下载内容

会下载所有期数的周刊文件：
- issue-1.md（第1期）
- issue-2.md（第2期）
- ...
- issue-361.md（最新期）

所有文件直接保存在指定文件夹中，无额外目录结构。
