const fs = require('fs');
const path = require('path');
const https = require('https');

// 目标文件夹 - 你可以修改这个路径
const TARGET_DIR = 'E:\\阮一峰周刊';

// GitHub API 基础 URL
const GITHUB_API_BASE = 'https://api.github.com/repos/ruanyf/weekly/contents/docs';
const GITHUB_RAW_BASE = 'https://raw.githubusercontent.com/ruanyf/weekly/master/docs';

/**
 * 创建目录（如果不存在）
 */
function ensureDirectoryExists(dirPath) {
    if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
        console.log(`✅ 创建目录: ${dirPath}`);
    }
}

/**
 * 下载文件
 */
function downloadFile(url, filePath) {
    return new Promise((resolve, reject) => {
        const file = fs.createWriteStream(filePath);
        
        https.get(url, (response) => {
            if (response.statusCode !== 200) {
                reject(new Error(`下载失败: ${response.statusCode} ${response.statusMessage}`));
                return;
            }
            
            response.pipe(file);
            
            file.on('finish', () => {
                file.close();
                resolve();
            });
            
            file.on('error', (err) => {
                fs.unlink(filePath, () => {}); // 删除部分下载的文件
                reject(err);
            });
        }).on('error', (err) => {
            reject(err);
        });
    });
}

/**
 * 获取仓库中的文件列表
 */
function getFileList() {
    return new Promise((resolve, reject) => {
        const url = GITHUB_API_BASE;
        
        https.get(url, {
            headers: {
                'User-Agent': 'Weekly-Downloader'
            }
        }, (response) => {
            let data = '';
            
            response.on('data', (chunk) => {
                data += chunk;
            });
            
            response.on('end', () => {
                try {
                    const files = JSON.parse(data);
                    resolve(files);
                } catch (err) {
                    reject(err);
                }
            });
        }).on('error', (err) => {
            reject(err);
        });
    });
}

/**
 * 主函数
 */
async function main() {
    try {
        console.log('🚀 开始下载阮一峰技术周刊...');
        console.log(`📁 目标目录: ${TARGET_DIR}`);
        
        // 确保目标目录存在
        ensureDirectoryExists(TARGET_DIR);
        
        // 获取文件列表
        console.log('📋 获取文件列表...');
        const files = await getFileList();
        
        // 筛选出 issue 开头的 md 文件
        const issueFiles = files.filter(file => 
            file.type === 'file' && 
            file.name.startsWith('issue-') && 
            file.name.endsWith('.md')
        );
        
        console.log(`📄 找到 ${issueFiles.length} 个周刊文件`);
        
        // 下载文件
        let successCount = 0;
        let failCount = 0;
        
        for (let i = 0; i < issueFiles.length; i++) {
            const file = issueFiles[i];
            const fileName = file.name;
            const filePath = path.join(TARGET_DIR, fileName);
            const downloadUrl = `${GITHUB_RAW_BASE}/${fileName}`;
            
            try {
                // 检查文件是否已存在
                if (fs.existsSync(filePath)) {
                    console.log(`⏭️  跳过已存在的文件: ${fileName}`);
                    successCount++;
                    continue;
                }
                
                console.log(`⬇️  下载中 (${i + 1}/${issueFiles.length}): ${fileName}`);
                await downloadFile(downloadUrl, filePath);
                console.log(`✅ 下载完成: ${fileName}`);
                successCount++;
                
                // 添加小延迟，避免请求过于频繁
                await new Promise(resolve => setTimeout(resolve, 100));
                
            } catch (error) {
                console.error(`❌ 下载失败: ${fileName} - ${error.message}`);
                failCount++;
            }
        }
        
        console.log('\n📊 下载统计:');
        console.log(`✅ 成功: ${successCount} 个文件`);
        console.log(`❌ 失败: ${failCount} 个文件`);
        console.log(`📁 文件保存在: ${TARGET_DIR}`);
        
        if (failCount === 0) {
            console.log('🎉 所有文件下载完成！');
        } else {
            console.log('⚠️  部分文件下载失败，请检查网络连接后重新运行');
        }
        
    } catch (error) {
        console.error('💥 程序执行出错:', error.message);
        process.exit(1);
    }
}

// 直接运行主函数
main();
